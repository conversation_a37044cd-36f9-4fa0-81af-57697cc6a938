"""
API route for train / model_run stats.

This module provides endpoints for accessing training run data including:
- Training metrics and statistics
- Training logs
- Generated plots and visualizations

Note: The `request: Request` parameter is included in all endpoints for future extensibility:
- Multi-environment support (extracting Supabase profile from headers)
- Authentication and authorization (access to auth headers, user context)
- Request logging and monitoring (client IP, user agent, performance metrics)
- Content negotiation (accept headers, language preferences)
- Rate limiting (client identification)
- Custom headers (feature flags, debugging, configuration)
"""

import json
import mimetypes
from typing import Any, Dict

from fastapi import APIRouter, HTTPException, Request, status
from fastapi.responses import FileResponse
from pydantic import BaseModel, ConfigDict, Field

from api.utils.error_handlers import INTERNAL_SERVER_ERROR_RESPONSE, api_route_handler
from src.config.paths import get_run_paths

router = APIRouter(
    prefix="",
    tags=["train"],
    responses=INTERNAL_SERVER_ERROR_RESPONSE,
)


class ModelRunStatsResponse(BaseModel):
    """Summary metrics for the model run."""

    summary: Dict[str, Any] = Field(
        ..., description="Summary metrics for the model run"
    )
    history: Dict[str, Any] = Field(
        ..., description="Detailed metrics history for the model run"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "summary": {
                    "final": {"train_loss": 0.69, "train_accuracy": 0.49},
                    "timing": {"total_training_time": 16.4},
                    "resources": {"cpu_percent_max": 37.1},
                },
                "history": {
                    "batch_metrics": [
                        {
                            "epoch": 0,
                            "batch": 0,
                            "loss": 2.67,
                            "accuracy": 0.43,
                            "time": 0.65,
                        },
                        # ...
                    ]
                },
            }
        }
    )


class ModelRunLogsResponse(BaseModel):
    """Log files for the model run, as a mapping of filename to content."""

    logs: Dict[str, str] = Field(
        ...,
        description="Log files for the model run, as a mapping of filename to content",
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "logs": {
                    "trained_model_script_training.log": "2025-06-23 16:38:52 - ..."
                }
            }
        }
    )


class ModelRunPlotsResponse(BaseModel):
    """Available plot files for the model run, as a mapping of filename to accessible URL.

    This endpoint only returns plots that cannot be recreated from the /stats endpoint data.
    Plots excluded (available via /stats data):
    - Training metrics: losses.png, accuracy.png, classification_metrics.png
    - Resource usage: resources.png, timing.png

    Available plot types include:
    - Test samples: test_images.png (prediction evolution visualization)
    - Augmentations: test_images_augmentation_comparison.png (original vs augmented)
    - Feature maps: feature_maps.png, feature_map_correlations.png

    Each plot can be fetched individually using the provided URL for better performance
    and to allow on-demand loading.
    """

    plots: Dict[str, str] = Field(
        ...,
        description="Available plot files as a mapping of filename to accessible URL",
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "plots": {
                    "test_images.png": "/api/v1/train/<MODEL_RUN_UUID>/plots/test_images.png",
                    "test_images_augmentation_comparison.png": (
                        "/api/v1/train/<MODEL_RUN_UUID>/plots/"
                        "test_images_augmentation_comparison.png"
                    ),
                    "feature_maps.png": "/api/v1/train/<MODEL_RUN_UUID>/plots/feature_maps.png",
                    "feature_map_correlations.png": (
                        "/api/v1/train/<MODEL_RUN_UUID>/plots/feature_map_correlations.png"
                    ),
                }
            }
        }
    )


@router.get(
    "/{model_run_uuid}/stats",
    response_model=ModelRunStatsResponse,
    summary="Get Model Run Stats",
    description="Retrieves detailed stats and metrics for a specific model run.",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "description": "Model run stats not found (possibly cleaned)",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Stats for model_run_uuid not found or cleaned."
                    }
                }
            },
        }
    },
)
@api_route_handler("retrieving model run stats")
async def get_model_run_stats(  # pylint: disable=unused-argument
    request: Request, model_run_uuid: str
) -> ModelRunStatsResponse:
    """Get detailed stats for a specific model run."""
    run_paths = get_run_paths(model_run_uuid)
    summary_path = run_paths.metrics_summary
    history_path = run_paths.metrics_history

    if not (summary_path.exists() and history_path.exists()):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Stats for {model_run_uuid} not found or cleaned.",
        )

    with open(summary_path, "r", encoding="utf-8") as f:
        summary = json.load(f)
    with open(history_path, "r", encoding="utf-8") as f:
        history = json.load(f)

    return ModelRunStatsResponse(summary=summary, history=history)


@router.get(
    "/{model_run_uuid}/logs",
    response_model=ModelRunLogsResponse,
    summary="Get Model Run Logs",
    description="Retrieves all log files for a specific model run.",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "description": "Model run logs not found (possibly cleaned)",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Logs for model_run_uuid not found or cleaned."
                    }
                }
            },
        }
    },
)
@api_route_handler("retrieving model run logs")
async def get_model_run_logs(  # pylint: disable=unused-argument
    request: Request, model_run_uuid: str
) -> ModelRunLogsResponse:
    """Get all logs for a specific model run."""
    run_paths = get_run_paths(model_run_uuid)
    logs_dir = run_paths.logs

    if not logs_dir.is_dir():
        # If logs directory is missing, return empty logs dict (not 404)
        return ModelRunLogsResponse(logs={})

    logs = {}
    for fpath in logs_dir.iterdir():
        if fpath.is_file():
            try:
                with open(fpath, "r", encoding="utf-8") as lf:
                    logs[fpath.name] = lf.read()
            except Exception:
                logs[fpath.name] = "[Could not read log file]"

    return ModelRunLogsResponse(logs=logs)


@router.get(
    "/{model_run_uuid}/plots",
    response_model=ModelRunPlotsResponse,
    summary="Get Model Run Plots List",
    description="Retrieves a list of available plot files for a specific model run "
    "returning URLs to access each plot. "
    "Only includes plots that cannot be recreated from /stats endpoint data "
    "(test samples, augmentations, feature maps). "
    "Training metrics plots (losses, accuracy, classification_metrics, resources, timing) "
    "are excluded as they can be recreated client-side from /stats data. "
    "Use the returned URLs to fetch individual plots.",
    responses={
        status.HTTP_200_OK: {
            "description": "List of available plots with URLs",
            "content": {
                "application/json": {
                    "example": {
                        "plots": {
                            "test_images.png": (
                                "/api/v1/train/<MODEL_RUN_UUID>/plots/test_images.png"
                            ),
                            "test_images_augmentation_comparison.png": (
                                "/api/v1/train/<MODEL_RUN_UUID>/plots/"
                                "test_images_augmentation_comparison.png"
                            ),
                            "feature_maps.png": (
                                "/api/v1/train/<MODEL_RUN_UUID>/plots/feature_maps.png"
                            ),
                            "feature_map_correlations.png": (
                                "/api/v1/train/<MODEL_RUN_UUID>/plots/feature_map_correlations.png"
                            ),
                        }
                    }
                }
            },
        }
    },
)
@api_route_handler("retrieving model run plots")
async def get_model_run_plots(  # pylint: disable=unused-argument
    request: Request, model_run_uuid: str
) -> ModelRunPlotsResponse:
    """Get available plots that cannot be recreated from /stats data."""
    run_paths = get_run_paths(model_run_uuid)
    plots_dir = run_paths.plots

    if not plots_dir.is_dir():
        # If plots directory is missing, return empty plots dict (not 404)
        return ModelRunPlotsResponse(plots={})

    # Define plots that can be recreated from /stats data and should be excluded
    excluded_plots = {
        "losses.png",  # Can be recreated from train.losses + test.losses
        "accuracy.png",  # Can be recreated from train.accuracies + test.accuracies
        "classification_metrics.png",  # Can be recreated from classification metrics
        "resources.png",  # Can be recreated from resources dict
        "timing.png",  # Can be recreated from timing dict
    }

    plots = {}
    for fpath in plots_dir.iterdir():
        if fpath.is_file() and fpath.suffix.lower() in [
            ".png",
            ".jpg",
            ".jpeg",
            ".svg",
        ]:
            # Only include plots that cannot be recreated from /stats data
            if fpath.name not in excluded_plots:
                # Construct URL for this plot file
                plot_url = f"/api/v1/train/{model_run_uuid}/plots/{fpath.name}"
                plots[fpath.name] = plot_url

    return ModelRunPlotsResponse(plots=plots)


@router.get(
    "/{model_run_uuid}/plots/{filename}",
    summary="Get Individual Plot Image",
    description="Retrieves a specific plot image file for a model run.",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "description": "Plot file not found",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Plot file 'filename' not found for model run."
                    }
                }
            },
        }
    },
)
@api_route_handler("retrieving individual plot image")
async def get_model_run_plot_image(  # pylint: disable=unused-argument
    request: Request, model_run_uuid: str, filename: str
) -> FileResponse:
    """Get a specific plot image file for a model run."""
    run_paths = get_run_paths(model_run_uuid)
    plots_dir = run_paths.plots
    plot_file_path = plots_dir / filename

    # Security check: ensure the filename doesn't contain path traversal attempts
    if ".." in filename or "/" in filename or "\\" in filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid filename: path traversal not allowed.",
        )

    # Check if the file exists and is actually a file
    if not plot_file_path.exists() or not plot_file_path.is_file():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Plot file '{filename}' not found for model run {model_run_uuid}.",
        )

    # Verify it's an image file by extension
    allowed_extensions = {".png", ".jpg", ".jpeg", ".svg"}
    if plot_file_path.suffix.lower() not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File '{filename}' is not a supported image format.",
        )

    # Determine the media type
    media_type, _ = mimetypes.guess_type(str(plot_file_path))
    if media_type is None:
        # Fallback based on extension
        extension_to_media_type = {
            ".png": "image/png",
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".svg": "image/svg+xml",
        }
        media_type = extension_to_media_type.get(
            plot_file_path.suffix.lower(), "application/octet-stream"
        )

    return FileResponse(
        path=str(plot_file_path), media_type=media_type, filename=filename
    )
